#!/usr/bin/env python3
"""
<PERSON>ript to organize image files by character type and update the config file accordingly.

This script:
1. Reads the assets.json file to get character data with types
2. Creates subdirectories in assets/ for each type (female_adult, female_kid, male_adult, male_kid, other)
3. Moves image files to their corresponding type directories
4. Updates the image_path in assets.json to reflect the new directory structure
"""

import json
import os
import shutil
from pathlib import Path
from collections import defaultdict

def main():
    # Define paths
    assets_json_path = "characters.json"
    assets_dir = Path("assets")
    
    # Load the assets.json file
    print("Loading assets.json...")
    with open(assets_json_path, 'r', encoding='utf-8') as f:
        assets_data = json.load(f)
    
    # Get all unique types
    types = set()
    for item in assets_data:
        if 'type' in item:
            types.add(item['type'])
    
    print(f"Found types: {sorted(types)}")
    
    # Create type directories
    print("Creating type directories...")
    for type_name in types:
        type_dir = assets_dir / type_name
        type_dir.mkdir(exist_ok=True)
        print(f"  Created/verified directory: {type_dir}")
    
    # Track statistics
    stats = defaultdict(int)
    moved_files = []
    missing_files = []
    
    # Process each character entry
    print("\nProcessing character entries...")
    for i, item in enumerate(assets_data):
        if 'image_path' not in item or 'type' not in item:
            print(f"  Skipping entry {i}: missing image_path or type")
            continue
        
        current_path = Path(item['image_path'])
        character_type = item['type']
        
        # Check if file exists
        if not current_path.exists():
            missing_files.append(str(current_path))
            print(f"  Warning: File not found: {current_path}")
            continue
        
        # Skip if already in correct directory
        if current_path.parent.name == character_type:
            print(f"  Already in correct directory: {current_path}")
            stats[f"{character_type}_already_correct"] += 1
            continue
        
        # Determine new path
        filename = current_path.name
        new_path = assets_dir / character_type / filename
        
        # Handle filename conflicts
        counter = 1
        original_new_path = new_path
        while new_path.exists() and new_path != current_path:
            stem = original_new_path.stem
            suffix = original_new_path.suffix
            new_path = original_new_path.parent / f"{stem}_{counter}{suffix}"
            counter += 1
        
        try:
            # Move the file
            print(f"  Moving: {current_path} -> {new_path}")
            shutil.move(str(current_path), str(new_path))
            
            # Update the assets data
            item['image_path'] = str(new_path)
            
            moved_files.append((str(current_path), str(new_path)))
            stats[f"{character_type}_moved"] += 1
            
        except Exception as e:
            print(f"  Error moving {current_path}: {e}")
            stats["errors"] += 1
    
    # Save updated assets.json
    print(f"\nSaving updated assets.json...")
    with open(assets_json_path, 'w', encoding='utf-8') as f:
        json.dump(assets_data, f, ensure_ascii=False, indent=2)
    
    # Print statistics
    print("\n" + "="*50)
    print("ORGANIZATION COMPLETE")
    print("="*50)
    
    print(f"\nStatistics:")
    for key, value in sorted(stats.items()):
        print(f"  {key}: {value}")
    
    print(f"\nTotal files moved: {len(moved_files)}")
    print(f"Missing files: {len(missing_files)}")
    
    if missing_files:
        print(f"\nMissing files:")
        for file in missing_files[:10]:  # Show first 10
            print(f"  {file}")
        if len(missing_files) > 10:
            print(f"  ... and {len(missing_files) - 10} more")
    
    # Show directory structure
    print(f"\nFinal directory structure:")
    for type_name in sorted(types):
        type_dir = assets_dir / type_name
        if type_dir.exists():
            file_count = len(list(type_dir.glob("*")))
            print(f"  {type_name}/: {file_count} files")
    
    print(f"\nScript completed successfully!")

if __name__ == "__main__":
    main()
